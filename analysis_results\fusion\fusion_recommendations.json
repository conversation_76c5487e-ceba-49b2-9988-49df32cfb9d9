{"pm25": {"weighted_average": {"method": "weighted_average", "weights": {"lgbm": 0.5668183888445048, "lstm": 0.24069111487664646, "prophet": 0.19249049627884884}, "description": "基于性能的加权平均"}, "top_models": {"method": "weighted_average", "weights": {"lgbm": 0.5668183888445048, "lstm": 0.24069111487664646}, "description": "仅使用前2名模型的加权平均"}, "equal_weight": {"method": "simple_average", "weights": {"lgbm": 0.3333333333333333, "lstm": 0.3333333333333333, "prophet": 0.3333333333333333}, "description": "等权重平均"}}, "o3": {"weighted_average": {"method": "weighted_average", "weights": {"lgbm": 0.4247840944010194, "lstm": 0.3533137667315653, "prophet": 0.22190213886741542}, "description": "基于性能的加权平均"}, "top_models": {"method": "weighted_average", "weights": {"lgbm": 0.4247840944010194, "lstm": 0.3533137667315653}, "description": "仅使用前2名模型的加权平均"}, "equal_weight": {"method": "simple_average", "weights": {"lgbm": 0.3333333333333333, "lstm": 0.3333333333333333, "prophet": 0.3333333333333333}, "description": "等权重平均"}}, "weather": {"weighted_average": {"method": "weighted_average", "weights": {"lgbm": 0.3179074446680081, "gru": 0.3440643863179075, "tcn": 0.33802816901408456}, "description": "基于性能的加权平均"}, "top_models": {"method": "weighted_average", "weights": {"gru": 0.3440643863179075, "tcn": 0.33802816901408456}, "description": "仅使用前2名模型的加权平均"}, "equal_weight": {"method": "simple_average", "weights": {"lgbm": 0.3333333333333333, "gru": 0.3333333333333333, "tcn": 0.3333333333333333}, "description": "等权重平均"}}}