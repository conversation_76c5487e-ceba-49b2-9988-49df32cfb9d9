#!/usr/bin/python
# _*_ coding: utf-8 _*_

"""
最优静态权重求解法 - 简化演示版本
"""

import numpy as np

def mean_absolute_error(y_true, y_pred):
    """计算平均绝对误差"""
    return np.mean(np.abs(y_true - y_pred))

def optimal_weights_solver(predictions_matrix, y_true):
    """
    最优静态权重求解器（网格搜索法）
    
    参数:
        predictions_matrix: 预测结果矩阵 (n_samples, n_models)
        y_true: 真实标签
    
    返回:
        最优权重数组, 最优损失
    """
    n_models = predictions_matrix.shape[1]
    
    if n_models == 3:
        # 三个模型的情况
        best_loss = float('inf')
        best_weights = None
        
        print("正在搜索最优权重...")
        
        # 网格搜索
        for i, w1 in enumerate(np.linspace(0, 1, 21)):  # 步长0.05
            for j, w2 in enumerate(np.linspace(0, 1-w1, 21)):
                w3 = 1 - w1 - w2
                if w3 < 0:
                    continue
                    
                weights = np.array([w1, w2, w3])
                weighted_pred = np.dot(predictions_matrix, weights)
                loss = mean_absolute_error(y_true, weighted_pred)
                
                if loss < best_loss:
                    best_loss = loss
                    best_weights = weights
        
        return best_weights, best_loss
    
    else:
        # 默认平均权重
        return np.ones(n_models) / n_models, float('inf')


def demonstrate_optimal_weights():
    """演示最优静态权重求解法"""
    print("=== 最优静态权重求解法演示 ===\n")
    
    # 1. 生成模拟数据
    np.random.seed(42)
    n_samples = 200
    
    # 真实函数：正弦波
    x = np.linspace(0, 4 * np.pi, n_samples)
    y_true = np.sin(x)
    
    # 三个不同质量的模型预测
    model1_pred = y_true + np.random.normal(0, 0.1, n_samples)  # 高质量
    model2_pred = y_true + np.random.normal(0, 0.2, n_samples)  # 中等质量  
    model3_pred = y_true + np.random.normal(0, 0.4, n_samples)  # 低质量
    
    # 分割数据
    train_size = int(n_samples * 0.7)
    
    # 训练集
    train_predictions = np.column_stack([
        model1_pred[:train_size],
        model2_pred[:train_size], 
        model3_pred[:train_size]
    ])
    y_train = y_true[:train_size]
    
    # 测试集
    test_predictions = np.column_stack([
        model1_pred[train_size:],
        model2_pred[train_size:],
        model3_pred[train_size:]
    ])
    y_test = y_true[train_size:]
    
    # 2. 计算各种权重方案
    print("1. 权重求解结果:")
    
    # 平均权重
    avg_weights = np.array([1/3, 1/3, 1/3])
    avg_pred = np.dot(test_predictions, avg_weights)
    avg_mae = mean_absolute_error(y_test, avg_pred)
    print(f"   平均权重: [{avg_weights[0]:.3f}, {avg_weights[1]:.3f}, {avg_weights[2]:.3f}]")
    print(f"   测试MAE: {avg_mae:.4f}")
    
    # 基于训练集性能的权重
    train_mae1 = mean_absolute_error(y_train, train_predictions[:, 0])
    train_mae2 = mean_absolute_error(y_train, train_predictions[:, 1])
    train_mae3 = mean_absolute_error(y_train, train_predictions[:, 2])
    
    print(f"\n   各模型在训练集上的MAE:")
    print(f"   高质量模型: {train_mae1:.4f}")
    print(f"   中等质量模型: {train_mae2:.4f}")
    print(f"   低质量模型: {train_mae3:.4f}")
    
    # 反比例权重（MAE越小权重越大）
    inv_maes = np.array([1/train_mae1, 1/train_mae2, 1/train_mae3])
    perf_weights = inv_maes / np.sum(inv_maes)
    perf_pred = np.dot(test_predictions, perf_weights)
    perf_mae = mean_absolute_error(y_test, perf_pred)
    print(f"\n   性能权重: [{perf_weights[0]:.3f}, {perf_weights[1]:.3f}, {perf_weights[2]:.3f}]")
    print(f"   测试MAE: {perf_mae:.4f}")
    
    # 最优静态权重求解法
    optimal_weights, optimal_loss = optimal_weights_solver(train_predictions, y_train)
    optimal_pred = np.dot(test_predictions, optimal_weights)
    optimal_mae = mean_absolute_error(y_test, optimal_pred)
    print(f"\n   最优权重: [{optimal_weights[0]:.3f}, {optimal_weights[1]:.3f}, {optimal_weights[2]:.3f}]")
    print(f"   训练损失: {optimal_loss:.4f}")
    print(f"   测试MAE: {optimal_mae:.4f}")
    
    # 3. 结果分析
    print(f"\n2. 关键发现:")
    print(f"   - 最优静态权重法相比平均权重减少了 {((avg_mae - optimal_mae)/avg_mae)*100:.1f}% 的误差")
    print(f"   - 最优静态权重法相比性能权重减少了 {((perf_mae - optimal_mae)/perf_mae)*100:.1f}% 的误差")
    print(f"   - 高质量模型获得了 {optimal_weights[0]:.1%} 的权重")
    print(f"   - 中等质量模型获得了 {optimal_weights[1]:.1%} 的权重")
    print(f"   - 低质量模型获得了 {optimal_weights[2]:.1%} 的权重")
    
    print(f"\n3. 方法对比:")
    methods = ['平均权重', '性能权重', '最优权重']
    maes = [avg_mae, perf_mae, optimal_mae]
    
    for method, mae in zip(methods, maes):
        print(f"   {method}: MAE = {mae:.4f}")
    
    best_method = methods[np.argmin(maes)]
    print(f"\n   🏆 最佳方法: {best_method}")
    
    print(f"\n4. 最优静态权重求解法的优势:")
    print(f"   ✓ 数学上最优：直接优化目标损失函数")
    print(f"   ✓ 自适应：根据具体数据找到最佳权重组合")
    print(f"   ✓ 可解释：权重分配符合模型质量")
    print(f"   ✓ 稳定：基于验证集性能，避免过拟合")


if __name__ == "__main__":
    demonstrate_optimal_weights()
