#!/usr/bin/python
# _*_ coding: utf-8 _*_

"""
最优静态权重求解法演示
"""

import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def mean_absolute_error(y_true, y_pred):
    """计算平均绝对误差"""
    return np.mean(np.abs(y_true - y_pred))

def mean_squared_error(y_true, y_pred):
    """计算均方误差"""
    return np.mean((y_true - y_pred) ** 2)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def simple_optimization_solver(predictions_matrix, y_true, loss_function='mse'):
    """
    简化版最优权重求解器（使用网格搜索）
    
    参数:
        predictions_matrix: 预测结果矩阵 (n_samples, n_models)
        y_true: 真实标签
        loss_function: 损失函数类型
    
    返回:
        最优权重数组
    """
    n_models = predictions_matrix.shape[1]
    
    if n_models == 2:
        # 两个模型的情况
        best_loss = float('inf')
        best_weights = None
        
        # 网格搜索
        for w1 in np.linspace(0, 1, 101):  # 0.00, 0.01, ..., 1.00
            w2 = 1 - w1
            weights = np.array([w1, w2])
            
            # 计算加权预测
            weighted_pred = np.dot(predictions_matrix, weights)
            
            # 计算损失
            if loss_function == 'mse':
                loss = np.mean((y_true - weighted_pred) ** 2)
            elif loss_function == 'mae':
                loss = np.mean(np.abs(y_true - weighted_pred))
            
            if loss < best_loss:
                best_loss = loss
                best_weights = weights
        
        return best_weights, best_loss
    
    elif n_models == 3:
        # 三个模型的情况
        best_loss = float('inf')
        best_weights = None
        
        # 网格搜索（粗粒度）
        for w1 in np.linspace(0, 1, 21):  # 步长0.05
            for w2 in np.linspace(0, 1-w1, 21):
                w3 = 1 - w1 - w2
                if w3 < 0:
                    continue
                    
                weights = np.array([w1, w2, w3])
                weighted_pred = np.dot(predictions_matrix, weights)
                
                if loss_function == 'mse':
                    loss = np.mean((y_true - weighted_pred) ** 2)
                elif loss_function == 'mae':
                    loss = np.mean(np.abs(y_true - weighted_pred))
                
                if loss < best_loss:
                    best_loss = loss
                    best_weights = weights
        
        return best_weights, best_loss
    
    else:
        # 默认平均权重
        return np.ones(n_models) / n_models, float('inf')


def demonstrate_optimal_static_weights():
    """演示最优静态权重求解法"""
    print("=== 最优静态权重求解法演示 ===\n")
    
    # 1. 生成模拟数据
    np.random.seed(42)
    n_samples = 200
    
    # 真实函数：正弦波 + 小幅噪声
    x = np.linspace(0, 4 * np.pi, n_samples)
    y_true = np.sin(x) + 0.1 * np.sin(10 * x)
    
    # 三个不同质量的模型预测
    model1_pred = y_true + np.random.normal(0, 0.1, n_samples)  # 高质量
    model2_pred = y_true + np.random.normal(0, 0.2, n_samples)  # 中等质量  
    model3_pred = y_true + np.random.normal(0, 0.4, n_samples)  # 低质量
    
    # 分割数据
    train_size = int(n_samples * 0.7)
    
    # 训练集
    train_predictions = np.column_stack([
        model1_pred[:train_size],
        model2_pred[:train_size], 
        model3_pred[:train_size]
    ])
    y_train = y_true[:train_size]
    
    # 测试集
    test_predictions = np.column_stack([
        model1_pred[train_size:],
        model2_pred[train_size:],
        model3_pred[train_size:]
    ])
    y_test = y_true[train_size:]
    
    # 2. 计算各种权重方案
    print("1. 权重求解结果:")
    
    # 平均权重
    avg_weights = np.array([1/3, 1/3, 1/3])
    avg_pred = np.dot(test_predictions, avg_weights)
    avg_mae = mean_absolute_error(y_test, avg_pred)
    print(f"   平均权重: {avg_weights}, 测试MAE: {avg_mae:.4f}")
    
    # 基于训练集性能的权重
    train_mae1 = mean_absolute_error(y_train, train_predictions[:, 0])
    train_mae2 = mean_absolute_error(y_train, train_predictions[:, 1])
    train_mae3 = mean_absolute_error(y_train, train_predictions[:, 2])
    
    # 反比例权重（MAE越小权重越大）
    inv_maes = np.array([1/train_mae1, 1/train_mae2, 1/train_mae3])
    perf_weights = inv_maes / np.sum(inv_maes)
    perf_pred = np.dot(test_predictions, perf_weights)
    perf_mae = mean_absolute_error(y_test, perf_pred)
    print(f"   性能权重: {perf_weights}, 测试MAE: {perf_mae:.4f}")
    
    # 最优静态权重（MSE损失）
    optimal_weights_mse, optimal_loss_mse = simple_optimization_solver(
        train_predictions, y_train, 'mse'
    )
    optimal_pred_mse = np.dot(test_predictions, optimal_weights_mse)
    optimal_mae_mse = mean_absolute_error(y_test, optimal_pred_mse)
    print(f"   最优权重(MSE): {optimal_weights_mse}, 测试MAE: {optimal_mae_mse:.4f}")
    
    # 最优静态权重（MAE损失）
    optimal_weights_mae, optimal_loss_mae = simple_optimization_solver(
        train_predictions, y_train, 'mae'
    )
    optimal_pred_mae = np.dot(test_predictions, optimal_weights_mae)
    optimal_mae_mae = mean_absolute_error(y_test, optimal_pred_mae)
    print(f"   最优权重(MAE): {optimal_weights_mae}, 测试MAE: {optimal_mae_mae:.4f}")
    
    # 3. 可视化结果
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 子图1：权重对比
    ax1 = axes[0, 0]
    methods = ['平均权重', '性能权重', '最优权重(MSE)', '最优权重(MAE)']
    weights_data = [avg_weights, perf_weights, optimal_weights_mse, optimal_weights_mae]
    
    x = np.arange(3)  # 三个模型
    width = 0.2
    
    for i, (method, weights) in enumerate(zip(methods, weights_data)):
        ax1.bar(x + i*width, weights, width, label=method)
    
    ax1.set_xlabel('模型')
    ax1.set_ylabel('权重')
    ax1.set_title('不同方法的权重分布')
    ax1.set_xticks(x + width * 1.5)
    ax1.set_xticklabels(['高质量模型', '中等质量模型', '低质量模型'])
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2：MAE对比
    ax2 = axes[0, 1]
    mae_scores = [avg_mae, perf_mae, optimal_mae_mse, optimal_mae_mae]
    colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold']
    
    bars = ax2.bar(methods, mae_scores, color=colors)
    ax2.set_ylabel('测试集MAE')
    ax2.set_title('不同权重方法的性能对比')
    ax2.tick_params(axis='x', rotation=45)
    
    # 在柱状图上标注数值
    for bar, mae in zip(bars, mae_scores):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                f'{mae:.4f}', ha='center', va='bottom')
    
    # 子图3：预测结果对比
    ax3 = axes[1, 0]
    test_indices = range(len(y_test))
    
    ax3.plot(test_indices, y_test, 'k-', linewidth=2, label='真实值')
    ax3.plot(test_indices, avg_pred, '--', alpha=0.7, label=f'平均权重 (MAE:{avg_mae:.3f})')
    ax3.plot(test_indices, optimal_pred_mae, 'r-', linewidth=2, 
             label=f'最优权重 (MAE:{optimal_mae_mae:.3f})')
    
    ax3.set_xlabel('样本索引')
    ax3.set_ylabel('预测值')
    ax3.set_title('预测结果对比（部分测试集）')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_xlim(0, 50)  # 只显示前50个点
    
    # 子图4：误差分布
    ax4 = axes[1, 1]
    
    avg_errors = np.abs(y_test - avg_pred)
    optimal_errors = np.abs(y_test - optimal_pred_mae)
    
    ax4.hist(avg_errors, bins=20, alpha=0.7, label='平均权重误差', color='skyblue')
    ax4.hist(optimal_errors, bins=20, alpha=0.7, label='最优权重误差', color='red')
    
    ax4.set_xlabel('绝对误差')
    ax4.set_ylabel('频次')
    ax4.set_title('误差分布对比')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimal_static_weights_demo.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n2. 关键发现:")
    print(f"   - 最优静态权重法相比平均权重减少了 {((avg_mae - optimal_mae_mae)/avg_mae)*100:.1f}% 的误差")
    print(f"   - 高质量模型获得了 {optimal_weights_mae[0]:.3f} 的权重")
    print(f"   - 低质量模型获得了 {optimal_weights_mae[2]:.3f} 的权重")
    print(f"   - 这符合我们的预期：质量越高的模型应该获得更大的权重")


if __name__ == "__main__":
    demonstrate_optimal_static_weights()
