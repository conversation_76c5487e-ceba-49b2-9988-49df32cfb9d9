#!/usr/bin/python
# _*_ coding: utf-8 _*_

"""
模型融合模块 - 实现多种模型融合策略以提高预测精度
"""

import os
import numpy as np
import pandas as pd
import logging
import joblib
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.metrics import (
    mean_absolute_error,
    mean_squared_error,
    r2_score,
    accuracy_score,
    f1_score,
)
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import json
from scipy.optimize import minimize
from scipy.optimize import differential_evolution
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    filename="model_fusion.log",
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

# 模型目录
MODEL_DIR = "trained_models"
# 融合模型保存目录
FUSION_DIR = os.path.join(MODEL_DIR, "fusion_models")
os.makedirs(FUSION_DIR, exist_ok=True)

# 评估指标缓存文件
METRICS_FILE = os.path.join(MODEL_DIR, "model_metrics.json")


def load_model_metrics():
    """加载模型评估指标，用于确定模型权重"""
    try:
        with open(METRICS_FILE, "r", encoding="utf-8") as f:
            metrics = json.load(f)
        logging.info(f"成功加载模型评估指标: {METRICS_FILE}")
        return metrics
    except Exception as e:
        logging.error(f"加载模型评估指标失败: {e}")
        return None


def calculate_model_weights(metrics, target, metric_name="mae", inverse=True):
    """
    根据模型评估指标计算权重

    参数:
        metrics: 模型评估指标字典
        target: 目标变量名称（如'avg_temp', 'aqi_index'等）
        metric_name: 用于计算权重的指标名称，默认为'mae'
        inverse: 是否对指标进行反向处理，对于误差类指标应为True

    返回:
        各模型的权重字典
    """
    weights = {}

    # 从metrics字典中获取与目标相关的模型和指标
    target_metrics = {}

    # 适配当前model_metrics.json的格式
    # 当前格式是扁平化的: {"model_target_metric": value} 而不是嵌套的
    for key, value in metrics.items():
        parts = key.split("_")

        # 跳过不符合格式的键
        if len(parts) < 3:
            continue

        model_name = parts[0]  # 例如: lgbm, lstm, prophet, gru, tcn
        model_target = parts[1]  # 例如: avg_temp, aqi_index, pm25, o3, weather
        model_metric = "_".join(parts[2:])  # 例如: mae, rmse, r2, accuracy

        # 如果是目标变量且指标匹配
        if model_target == target and model_metric == metric_name:
            target_metrics[model_name] = value

    if not target_metrics:
        logging.warning(f"未找到 {target} 的 {metric_name} 指标，使用平均权重")

        # 找出所有处理这个目标的模型
        related_models = set()
        for key in metrics.keys():
            parts = key.split("_")
            if len(parts) >= 2 and parts[1] == target:
                related_models.add(parts[0])

        if related_models:
            return {model: 1.0 / len(related_models) for model in related_models}
        else:
            # 退回到默认模型
            if target == "weather":
                return {"lgbm": 0.3, "gru": 0.4, "tcn": 0.3}
            else:
                return {"lgbm": 0.4, "lstm": 0.3, "prophet": 0.3}

    # 根据指标计算权重
    if inverse:
        # 对于误差类指标，值越小越好，需要取倒数
        # 对极小值进行处理以避免权重过大
        epsilon = 1e-10
        sum_inverse = sum(
            1.0 / (metric + epsilon) for metric in target_metrics.values()
        )
        for model, metric in target_metrics.items():
            weights[model] = (1.0 / (metric + epsilon)) / sum_inverse
    else:
        # 对于准确率类指标，值越大越好，直接归一化
        sum_metrics = sum(target_metrics.values())
        for model, metric in target_metrics.items():
            weights[model] = metric / sum_metrics

    logging.info(f"计算 {target} 的模型权重: {weights}")
    return weights


def optimal_static_weights_solver(predictions_dict, y_true, loss_function='mse', method='SLSQP'):
    """
    最优静态权重求解法 - 通过数学优化求解最优权重组合

    参数:
        predictions_dict: 字典，键为模型名称，值为模型在验证集上的预测结果
        y_true: 验证集的真实标签
        loss_function: 损失函数类型 ('mse', 'mae', 'cross_entropy')
        method: 优化方法 ('SLSQP', 'differential_evolution', 'grid_search')

    返回:
        最优权重字典
    """
    if not predictions_dict or y_true is None:
        logging.error("没有提供足够的数据进行最优权重求解")
        return None

    model_names = list(predictions_dict.keys())
    n_models = len(model_names)

    # 将预测结果转换为矩阵形式 (n_samples, n_models)
    predictions_matrix = np.column_stack([predictions_dict[name] for name in model_names])

    def objective_function(weights):
        """目标函数：计算加权融合后的损失"""
        # 确保权重和为1
        weights = weights / np.sum(weights)

        # 计算加权预测
        weighted_pred = np.dot(predictions_matrix, weights)

        # 根据损失函数类型计算损失
        if loss_function == 'mse':
            loss = np.mean((y_true - weighted_pred) ** 2)
        elif loss_function == 'mae':
            loss = np.mean(np.abs(y_true - weighted_pred))
        elif loss_function == 'cross_entropy':
            # 对于分类问题，假设y_true是类别标签，weighted_pred是概率
            epsilon = 1e-15
            weighted_pred = np.clip(weighted_pred, epsilon, 1 - epsilon)
            loss = -np.mean(y_true * np.log(weighted_pred) + (1 - y_true) * np.log(1 - weighted_pred))
        else:
            raise ValueError(f"不支持的损失函数: {loss_function}")

        return loss

    # 约束条件：权重非负且和为1
    constraints = [
        {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}  # 权重和为1
    ]
    bounds = [(0.0, 1.0) for _ in range(n_models)]  # 权重范围[0,1]

    # 初始权重：平均权重
    initial_weights = np.ones(n_models) / n_models

    try:
        if method == 'SLSQP':
            # 使用序列最小二乘规划法
            result = minimize(
                objective_function,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            optimal_weights = result.x

        elif method == 'differential_evolution':
            # 使用差分进化算法（全局优化）
            def constrained_objective(weights):
                # 归一化权重
                weights = weights / np.sum(weights)
                return objective_function(weights)

            result = differential_evolution(
                constrained_objective,
                bounds,
                seed=42,
                maxiter=1000,
                atol=1e-8
            )
            optimal_weights = result.x
            optimal_weights = optimal_weights / np.sum(optimal_weights)  # 确保归一化

        elif method == 'grid_search':
            # 网格搜索法（适用于模型数量较少的情况）
            optimal_weights = _grid_search_weights(predictions_matrix, y_true, loss_function)

        else:
            raise ValueError(f"不支持的优化方法: {method}")

        # 确保权重归一化
        optimal_weights = optimal_weights / np.sum(optimal_weights)

        # 计算最优损失
        optimal_loss = objective_function(optimal_weights)

        # 构建结果字典
        weights_dict = {model_names[i]: optimal_weights[i] for i in range(n_models)}

        logging.info(f"最优静态权重求解完成:")
        logging.info(f"  优化方法: {method}")
        logging.info(f"  损失函数: {loss_function}")
        logging.info(f"  最优损失: {optimal_loss:.6f}")
        logging.info(f"  最优权重: {weights_dict}")

        return weights_dict

    except Exception as e:
        logging.error(f"最优权重求解失败: {e}")
        # 返回平均权重作为备选
        return {model_names[i]: 1.0/n_models for i in range(n_models)}


def _grid_search_weights(predictions_matrix, y_true, loss_function, grid_size=21):
    """
    网格搜索法求解最优权重（适用于2-3个模型）

    参数:
        predictions_matrix: 预测结果矩阵
        y_true: 真实标签
        loss_function: 损失函数类型
        grid_size: 网格大小

    返回:
        最优权重数组
    """
    n_models = predictions_matrix.shape[1]

    if n_models == 2:
        # 两个模型的情况：w1 + w2 = 1, 只需搜索w1
        best_loss = float('inf')
        best_weights = None

        for w1 in np.linspace(0, 1, grid_size):
            w2 = 1 - w1
            weights = np.array([w1, w2])

            weighted_pred = np.dot(predictions_matrix, weights)

            if loss_function == 'mse':
                loss = np.mean((y_true - weighted_pred) ** 2)
            elif loss_function == 'mae':
                loss = np.mean(np.abs(y_true - weighted_pred))
            else:
                continue

            if loss < best_loss:
                best_loss = loss
                best_weights = weights

        return best_weights

    elif n_models == 3:
        # 三个模型的情况：w1 + w2 + w3 = 1
        best_loss = float('inf')
        best_weights = None

        for w1 in np.linspace(0, 1, grid_size):
            for w2 in np.linspace(0, 1-w1, grid_size):
                w3 = 1 - w1 - w2
                if w3 < 0:
                    continue

                weights = np.array([w1, w2, w3])
                weighted_pred = np.dot(predictions_matrix, weights)

                if loss_function == 'mse':
                    loss = np.mean((y_true - weighted_pred) ** 2)
                elif loss_function == 'mae':
                    loss = np.mean(np.abs(y_true - weighted_pred))
                else:
                    continue

                if loss < best_loss:
                    best_loss = loss
                    best_weights = weights

        return best_weights

    else:
        # 模型数量过多，使用随机搜索
        logging.warning(f"模型数量({n_models})过多，网格搜索退化为随机搜索")
        best_loss = float('inf')
        best_weights = None

        for _ in range(grid_size * 100):  # 增加搜索次数
            # 生成随机权重并归一化
            weights = np.random.random(n_models)
            weights = weights / np.sum(weights)

            weighted_pred = np.dot(predictions_matrix, weights)

            if loss_function == 'mse':
                loss = np.mean((y_true - weighted_pred) ** 2)
            elif loss_function == 'mae':
                loss = np.mean(np.abs(y_true - weighted_pred))
            else:
                continue

            if loss < best_loss:
                best_loss = loss
                best_weights = weights

        return best_weights


def compare_weight_methods(predictions_dict, y_true, target, is_classification=False):
    """
    比较不同权重求解方法的效果

    参数:
        predictions_dict: 字典，键为模型名称，值为模型预测结果
        y_true: 真实标签
        target: 目标变量名称
        is_classification: 是否为分类问题

    返回:
        包含不同方法权重和性能的字典
    """
    results = {}

    # 1. 平均权重
    n_models = len(predictions_dict)
    avg_weights = {model: 1.0/n_models for model in predictions_dict.keys()}
    avg_pred = weighted_average_fusion(predictions_dict, avg_weights)

    if is_classification:
        avg_score = accuracy_score(y_true, avg_pred)
        score_name = 'accuracy'
    else:
        avg_score = mean_absolute_error(y_true, avg_pred)
        score_name = 'mae'

    results['average'] = {
        'weights': avg_weights,
        'score': avg_score,
        'predictions': avg_pred
    }

    # 2. 基于历史性能的权重（现有方法）
    metrics = load_model_metrics()
    if metrics:
        perf_weights = calculate_model_weights(metrics, target, 'mae', True)
        if perf_weights:
            perf_pred = weighted_average_fusion(predictions_dict, perf_weights)
            if is_classification:
                perf_score = accuracy_score(y_true, perf_pred)
            else:
                perf_score = mean_absolute_error(y_true, perf_pred)

            results['performance_based'] = {
                'weights': perf_weights,
                'score': perf_score,
                'predictions': perf_pred
            }

    # 3. 最优静态权重求解法 - SLSQP
    loss_func = 'cross_entropy' if is_classification else 'mae'
    optimal_weights_slsqp = optimal_static_weights_solver(
        predictions_dict, y_true, loss_func, 'SLSQP'
    )
    if optimal_weights_slsqp:
        optimal_pred_slsqp = weighted_average_fusion(predictions_dict, optimal_weights_slsqp)
        if is_classification:
            optimal_score_slsqp = accuracy_score(y_true, optimal_pred_slsqp)
        else:
            optimal_score_slsqp = mean_absolute_error(y_true, optimal_pred_slsqp)

        results['optimal_slsqp'] = {
            'weights': optimal_weights_slsqp,
            'score': optimal_score_slsqp,
            'predictions': optimal_pred_slsqp
        }

    # 4. 最优静态权重求解法 - 差分进化
    optimal_weights_de = optimal_static_weights_solver(
        predictions_dict, y_true, loss_func, 'differential_evolution'
    )
    if optimal_weights_de:
        optimal_pred_de = weighted_average_fusion(predictions_dict, optimal_weights_de)
        if is_classification:
            optimal_score_de = accuracy_score(y_true, optimal_pred_de)
        else:
            optimal_score_de = mean_absolute_error(y_true, optimal_pred_de)

        results['optimal_differential_evolution'] = {
            'weights': optimal_weights_de,
            'score': optimal_score_de,
            'predictions': optimal_pred_de
        }

    # 5. 网格搜索法（如果模型数量不多）
    if len(predictions_dict) <= 3:
        optimal_weights_grid = optimal_static_weights_solver(
            predictions_dict, y_true, loss_func, 'grid_search'
        )
        if optimal_weights_grid:
            optimal_pred_grid = weighted_average_fusion(predictions_dict, optimal_weights_grid)
            if is_classification:
                optimal_score_grid = accuracy_score(y_true, optimal_pred_grid)
            else:
                optimal_score_grid = mean_absolute_error(y_true, optimal_pred_grid)

            results['optimal_grid_search'] = {
                'weights': optimal_weights_grid,
                'score': optimal_score_grid,
                'predictions': optimal_pred_grid
            }

    # 找出最佳方法
    if is_classification:
        # 分类问题：准确率越高越好
        best_method = max(results.keys(), key=lambda k: results[k]['score'])
    else:
        # 回归问题：MAE越小越好
        best_method = min(results.keys(), key=lambda k: results[k]['score'])

    results['best_method'] = best_method
    results['best_weights'] = results[best_method]['weights']

    logging.info(f"权重方法比较完成 - {target}:")
    for method, result in results.items():
        if method not in ['best_method', 'best_weights']:
            logging.info(f"  {method}: {score_name}={result['score']:.4f}, weights={result['weights']}")
    logging.info(f"  最佳方法: {best_method}")

    return results


def simple_average_fusion(predictions):
    """
    简单平均融合

    参数:
        predictions: 字典，键为模型名称，值为模型预测结果数组

    返回:
        融合后的预测结果数组
    """
    if not predictions:
        logging.error("没有提供预测结果进行融合")
        return None

    # 提取所有预测结果
    pred_arrays = list(predictions.values())

    # 检查预测结果维度是否一致
    shapes = [arr.shape for arr in pred_arrays]
    if len(set(shapes)) > 1:
        logging.error(f"预测结果维度不一致: {shapes}")
        return None

    # 计算平均值
    fusion_result = np.mean(pred_arrays, axis=0)
    logging.info(f"简单平均融合完成，融合 {len(predictions)} 个模型的预测结果")

    return fusion_result


def weighted_average_fusion(predictions, weights=None):
    """
    加权平均融合

    参数:
        predictions: 字典，键为模型名称，值为模型预测结果数组
        weights: 字典，键为模型名称，值为权重

    返回:
        融合后的预测结果数组
    """
    if not predictions:
        logging.error("没有提供预测结果进行融合")
        return None

    # 如果未提供权重，使用平均权重
    if weights is None:
        weights = {model: 1.0 / len(predictions) for model in predictions.keys()}

    # 提取并检查权重
    model_names = list(predictions.keys())
    weight_values = np.array(
        [weights.get(model, 1.0 / len(predictions)) for model in model_names]
    )
    weight_sum = np.sum(weight_values)

    if weight_sum == 0:
        logging.warning("权重和为0，使用平均权重")
        weight_values = np.ones(len(model_names)) / len(model_names)
    else:
        # 归一化权重
        weight_values = weight_values / weight_sum

    # 提取所有预测结果
    pred_arrays = [predictions[model] for model in model_names]

    # 检查预测结果维度是否一致
    shapes = [arr.shape for arr in pred_arrays]
    if len(set(shapes)) > 1:
        logging.error(f"预测结果维度不一致: {shapes}")
        return None

    # 加权平均
    fusion_result = np.zeros_like(pred_arrays[0])
    for i, pred in enumerate(pred_arrays):
        fusion_result += pred * weight_values[i]

    logging.info(f"加权平均融合完成，权重: {dict(zip(model_names, weight_values))}")

    return fusion_result


def dynamic_weighted_fusion(predictions, features, target):
    """
    基于当前特征的动态加权融合

    参数:
        predictions: 字典，键为模型名称，值为模型预测结果数组
        features: 特征数据，用于动态调整权重
        target: 目标变量名称

    返回:
        融合后的预测结果数组
    """
    if not predictions or features is None:
        logging.error("没有提供足够的数据进行动态加权融合")
        return None

    # 基于特征的简单规则调整权重
    # 这里只是示例，实际应用中应根据模型性能在不同条件下的表现来定制规则

    # 提取一些可能有用的特征
    weights = {model: 1.0 for model in predictions.keys()}

    try:
        # 根据季节调整权重
        if "date" in features:
            dates = pd.to_datetime(features["date"])
            month = (
                dates.iloc[-1].month
                if isinstance(dates, pd.Series)
                else dates[-1].month
            )

            # 夏季(6-8月)增加LSTM权重，冬季(12-2月)增加LightGBM权重
            if 6 <= month <= 8:
                if "lstm" in weights:
                    weights["lstm"] *= 1.5
                if "prophet" in weights:
                    weights["prophet"] *= 0.8
            elif month == 12 or 1 <= month <= 2:
                if "lgbm" in weights:
                    weights["lgbm"] *= 1.5
                if "prophet" in weights:
                    weights["prophet"] *= 1.2

        # 根据目标变量调整
        if target == "avg_temp":
            if "lstm" in weights:
                weights["lstm"] *= 1.2
        elif target == "aqi_index":
            if "lgbm" in weights:
                weights["lgbm"] *= 1.2
        elif target == "pm25":
            if "lstm" in weights:
                weights["lstm"] *= 1.3
            if "prophet" in weights:
                weights["prophet"] *= 0.9
        elif target == "weather":  # 分类目标
            if "gru" in weights:
                weights["gru"] *= 1.4
            if "tcn" in weights:
                weights["tcn"] *= 1.2
    except Exception as e:
        logging.warning(f"动态调整权重时出错: {e}，使用原始权重")

    # 使用调整后的权重进行融合
    return weighted_average_fusion(predictions, weights)


def stacking_fusion_train(base_predictions, y_true, is_classification=False):
    """
    训练Stacking融合模型

    参数:
        base_predictions: 字典，键为模型名称，值为模型对训练集的预测结果
        y_true: 训练集的真实标签
        is_classification: 是否为分类问题

    返回:
        训练好的元模型
    """
    if not base_predictions or y_true is None:
        logging.error("没有提供足够的数据训练Stacking模型")
        return None

    try:
        # 创建元特征矩阵
        meta_features = np.column_stack([pred for pred in base_predictions.values()])

        # 根据问题类型选择元模型
        if is_classification:
            meta_model = RandomForestClassifier(n_estimators=100, random_state=42)
        else:
            meta_model = RandomForestRegressor(n_estimators=100, random_state=42)

        # 训练元模型
        meta_model.fit(meta_features, y_true)

        logging.info(f"Stacking融合模型训练完成，特征维度: {meta_features.shape}")
        return meta_model

    except Exception as e:
        logging.error(f"训练Stacking模型失败: {e}")
        return None


def stacking_fusion_predict(base_predictions, meta_model):
    """
    使用Stacking融合模型进行预测

    参数:
        base_predictions: 字典，键为模型名称，值为模型对测试集的预测结果
        meta_model: 训练好的元模型

    返回:
        融合后的预测结果
    """
    if not base_predictions or meta_model is None:
        logging.error("没有提供足够的数据进行Stacking预测")
        return None

    try:
        # 创建元特征矩阵
        meta_features = np.column_stack([pred for pred in base_predictions.values()])

        # 使用元模型预测
        fusion_result = meta_model.predict(meta_features)

        logging.info(f"Stacking融合预测完成，特征维度: {meta_features.shape}")
        return fusion_result

    except Exception as e:
        logging.error(f"Stacking预测失败: {e}")
        return None


def save_fusion_model(model, target, fusion_type):
    """
    保存融合模型

    参数:
        model: 融合模型或权重
        target: 目标变量名称
        fusion_type: 融合类型 (如'stacking', 'weighted')
    """
    try:
        model_path = os.path.join(FUSION_DIR, f"{fusion_type}_{target}_model.pkl")
        joblib.dump(model, model_path)
        logging.info(f"融合模型已保存: {model_path}")
        return model_path
    except Exception as e:
        logging.error(f"保存融合模型失败: {e}")
        return None


def load_fusion_model(target, fusion_type):
    """
    加载融合模型

    参数:
        target: 目标变量名称
        fusion_type: 融合类型 (如'stacking', 'weighted')

    返回:
        加载的融合模型或权重
    """
    try:
        model_path = os.path.join(FUSION_DIR, f"{fusion_type}_{target}_model.pkl")
        model = joblib.load(model_path)
        logging.info(f"成功加载融合模型: {model_path}")
        return model
    except Exception as e:
        logging.error(f"加载融合模型失败: {e}")
        return None


def evaluate_fusion_models(
    y_true, predictions, fusion_predictions, is_classification=False
):
    """
    评估融合模型与单个模型的性能对比

    参数:
        y_true: 真实标签
        predictions: 字典，键为模型名称，值为模型预测结果
        fusion_predictions: 字典，键为融合方法名称，值为融合预测结果
        is_classification: 是否为分类问题

    返回:
        包含评估指标的字典
    """
    evaluation = {}

    # 评估单个模型
    for model_name, pred in predictions.items():
        if is_classification:
            accuracy = accuracy_score(y_true, pred)
            f1 = f1_score(y_true, pred, average="weighted")
            evaluation[model_name] = {"accuracy": accuracy, "f1_score": f1}
        else:
            mae = mean_absolute_error(y_true, pred)
            rmse = np.sqrt(mean_squared_error(y_true, pred))
            r2 = r2_score(y_true, pred)
            evaluation[model_name] = {"mae": mae, "rmse": rmse, "r2": r2}

    # 评估融合模型
    for fusion_name, pred in fusion_predictions.items():
        if is_classification:
            accuracy = accuracy_score(y_true, pred)
            f1 = f1_score(y_true, pred, average="weighted")
            evaluation[fusion_name] = {"accuracy": accuracy, "f1_score": f1}
        else:
            mae = mean_absolute_error(y_true, pred)
            rmse = np.sqrt(mean_squared_error(y_true, pred))
            r2 = r2_score(y_true, pred)
            evaluation[fusion_name] = {"mae": mae, "rmse": rmse, "r2": r2}

    logging.info(f"模型评估完成: {evaluation}")
    return evaluation


def plot_fusion_comparison(
    y_true, predictions, fusion_predictions, target, save_dir="analysis_results/fusion"
):
    """
    可视化展示融合模型与单个模型的性能对比

    参数:
        y_true: 真实标签
        predictions: 字典，键为模型名称，值为模型预测结果
        fusion_predictions: 字典，键为融合方法名称，值为融合预测结果
        target: 目标变量名称
        save_dir: 图表保存目录
    """
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)

    # 合并所有预测结果
    all_predictions = {**predictions, **fusion_predictions}

    # 是否为分类问题
    is_classification = (
        isinstance(y_true[0], (np.integer, int)) and len(np.unique(y_true)) < 20
    )

    # 评估所有模型
    evaluation = evaluate_fusion_models(
        y_true, predictions, fusion_predictions, is_classification
    )

    # 1. 创建预测结果对比图
    plt.figure(figsize=(15, 8))

    if not is_classification:
        # 回归问题：线图对比
        plt.plot(y_true, "k-", linewidth=2, label="真实值")

        for model_name, pred in all_predictions.items():
            # 区分融合模型和单个模型的线条样式
            if model_name in fusion_predictions:
                plt.plot(
                    pred,
                    linewidth=2,
                    label=f"{model_name} (MAE: {evaluation[model_name]['mae']:.3f})",
                )
            else:
                plt.plot(
                    pred,
                    "--",
                    alpha=0.7,
                    label=f"{model_name} (MAE: {evaluation[model_name]['mae']:.3f})",
                )

        plt.title(f"{target}预测结果对比", fontsize=15)
        plt.ylabel(target, fontsize=12)
        plt.grid(True, alpha=0.3)

    else:
        # 分类问题：混淆矩阵热图组
        # 这里简化处理，只展示预测准确率
        accuracy_scores = {
            model: evaluation[model]["accuracy"] for model in all_predictions.keys()
        }

        models = list(accuracy_scores.keys())
        accuracy_values = [accuracy_scores[model] for model in models]

        # 创建条形图
        plt.barh(models, accuracy_values, color=plt.cm.tab10.colors)
        plt.xlabel("准确率", fontsize=12)
        plt.title(f"{target}分类准确率对比", fontsize=15)

        # 在条形上标注具体值
        for i, v in enumerate(accuracy_values):
            plt.text(v + 0.01, i, f"{v:.3f}", va="center")

    plt.legend(loc="best")
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f"{target}_fusion_comparison.png"), dpi=300)
    plt.close()

    # 2. 创建误差指标对比图
    if not is_classification:
        plt.figure(figsize=(12, 6))

        # 提取MAE值
        mae_values = [evaluation[model]["mae"] for model in all_predictions.keys()]
        rmse_values = [evaluation[model]["rmse"] for model in all_predictions.keys()]

        # 设置条形图位置
        x = np.arange(len(all_predictions))
        width = 0.35

        # 绘制MAE和RMSE条形图
        plt.bar(x - width / 2, mae_values, width, label="MAE", color="skyblue")
        plt.bar(x + width / 2, rmse_values, width, label="RMSE", color="salmon")

        plt.xticks(x, all_predictions.keys(), rotation=45, ha="right")
        plt.ylabel("误差值", fontsize=12)
        plt.title(f"{target}预测误差对比", fontsize=15)
        plt.legend()
        plt.grid(axis="y", alpha=0.3)

        # 在顶部标注值
        for i, v in enumerate(mae_values):
            plt.text(
                i - width / 2,
                v + 0.02,
                f"{v:.3f}",
                ha="center",
                va="bottom",
                fontsize=9,
            )
        for i, v in enumerate(rmse_values):
            plt.text(
                i + width / 2,
                v + 0.02,
                f"{v:.3f}",
                ha="center",
                va="bottom",
                fontsize=9,
            )

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f"{target}_error_comparison.png"), dpi=300)
        plt.close()

    # 3. 保存评估指标
    eval_file = os.path.join(save_dir, f"{target}_fusion_evaluation.json")
    with open(eval_file, "w", encoding="utf-8") as f:
        json.dump(evaluation, f, indent=4, ensure_ascii=False)

    logging.info(f"融合模型比较图表已保存至 {save_dir}")


def create_fusion_pipeline(target):
    """
    为特定目标变量创建融合模型管道

    参数:
        target: 目标变量名称
    """
    logging.info(f"开始为 {target} 创建融合模型管道")

    # 1. 加载模型评估指标
    metrics = load_model_metrics()
    if metrics is None:
        logging.error("无法加载模型评估指标，融合管道创建失败")
        return False

    # 2. 计算基于性能的模型权重
    weights = calculate_model_weights(metrics, target)

    # 3. 保存权重作为加权融合模型
    if weights:
        saved_path = save_fusion_model(weights, target, "weighted")
        if saved_path:
            logging.info(f"加权融合权重已保存: {saved_path}")

    # 4. 保存最优静态权重配置
    optimal_config = {
        "enabled": True,
        "methods": ["SLSQP", "differential_evolution"],
        "loss_function": "cross_entropy" if target == "weather" else "mae",
        "fallback_weights": weights
    }
    saved_path = save_fusion_model(optimal_config, target, "optimal_static")
    if saved_path:
        logging.info(f"最优静态权重配置已保存: {saved_path}")

    # 5. 保存动态加权融合参数 (这里简单地保存一个配置标记)
    dynamic_config = {"enabled": True, "base_weights": weights}
    saved_path = save_fusion_model(dynamic_config, target, "dynamic")
    if saved_path:
        logging.info(f"动态融合配置已保存: {saved_path}")

    # 注意: Stacking融合需要在实际预测时训练，因为需要基础模型的预测结果和真实标签
    # 这里只保存一个占位符配置
    stacking_config = {"enabled": True, "is_classification": target == "weather"}
    saved_path = save_fusion_model(stacking_config, target, "stacking")
    if saved_path:
        logging.info(f"Stacking融合配置已保存: {saved_path}")

    logging.info(f"{target} 的融合模型管道创建完成")
    return True


def test_fusion_methods():
    """测试所有融合方法是否正常工作"""
    # 创建模拟数据
    np.random.seed(42)
    n_samples = 100

    # 生成三个模型的预测值
    model1_pred = np.sin(np.linspace(0, 4 * np.pi, n_samples)) + np.random.normal(
        0, 0.2, n_samples
    )
    model2_pred = np.sin(np.linspace(0, 4 * np.pi, n_samples)) + np.random.normal(
        0, 0.3, n_samples
    )
    model3_pred = np.sin(np.linspace(0, 4 * np.pi, n_samples)) + np.random.normal(
        0, 0.4, n_samples
    )

    # 真实值
    y_true = np.sin(np.linspace(0, 4 * np.pi, n_samples))

    # 构建预测字典
    predictions = {"model1": model1_pred, "model2": model2_pred, "model3": model3_pred}

    # 测试简单平均融合
    simple_fusion = simple_average_fusion(predictions)

    # 测试加权平均融合
    weights = {"model1": 0.5, "model2": 0.3, "model3": 0.2}
    weighted_fusion = weighted_average_fusion(predictions, weights)

    # 测试动态加权融合
    features = {"date": [datetime.now() + timedelta(days=i) for i in range(n_samples)]}
    dynamic_fusion = dynamic_weighted_fusion(predictions, features, "avg_temp")

    # 准备Stacking融合
    # 使用前80%数据训练元模型
    train_size = int(n_samples * 0.8)
    train_predictions = {
        "model1": model1_pred[:train_size],
        "model2": model2_pred[:train_size],
        "model3": model3_pred[:train_size],
    }
    y_train = y_true[:train_size]

    # 训练元模型
    meta_model = stacking_fusion_train(train_predictions, y_train)

    # 使用后20%数据测试
    test_predictions = {
        "model1": model1_pred[train_size:],
        "model2": model2_pred[train_size:],
        "model3": model3_pred[train_size:],
    }
    y_test = y_true[train_size:]

    # 预测
    stacking_result = None
    if meta_model is not None:
        stacking_result = stacking_fusion_predict(test_predictions, meta_model)

    # 收集所有融合结果
    fusion_results = {
        "simple_average": (
            simple_fusion[train_size:] if simple_fusion is not None else None
        ),
        "weighted_average": (
            weighted_fusion[train_size:] if weighted_fusion is not None else None
        ),
        "dynamic_weighted": (
            dynamic_fusion[train_size:] if dynamic_fusion is not None else None
        ),
        "stacking": stacking_result,
    }

    # 绘制比较图表
    plot_fusion_comparison(
        y_test,
        test_predictions,
        {k: v for k, v in fusion_results.items() if v is not None},
        "test_target",
    )

    logging.info("融合方法测试完成")
    return True


def create_all_fusion_models():
    """为所有支持的目标变量创建融合模型"""
    # 支持融合的目标变量
    regression_targets = ["avg_temp", "aqi_index", "pm25", "o3"]
    classification_targets = ["weather"]

    for target in regression_targets + classification_targets:
        success = create_fusion_pipeline(target)
        if success:
            logging.info(f"{target} 融合模型创建成功")
        else:
            logging.error(f"{target} 融合模型创建失败")

    return True


if __name__ == "__main__":
    logging.info("=== 开始模型融合模块测试 ===")
    test_fusion_methods()
    logging.info("=== 开始创建所有目标变量的融合模型 ===")
    create_all_fusion_models()
    logging.info("=== 模型融合模块处理完成 ===")
